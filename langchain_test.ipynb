import os
import getpass
import os
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="kimi2",
                  api_key="tc-lPYsGhv3Q4m1BUXt8140EdE0129f44E58704141e0c41B589",
                  base_url="http://aiinone.seasungame.com:8000/ai_in_one/v2/chat/completions")
os.environ["LANGSMITH_TRACING_v2"] = "true"
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ['LANGSMITH_PROJECT'] = "test"
os.environ['LANGSMITH_ENDPOINT'] = "https://api.smith.langchain.com"

from langchain_core.messages import HumanMessage

llm.invoke([HumanMessage(content="What's my name")])


from langchain_core.messages import AIMessage
llm.invoke(
    [
        HumanMessage(content="Hi! I'm <PERSON>"),
        AIMessage(content="Hello Bob! How can I assist you today?"),
        HumanMessage(content="What's my name?"),
    ]
)


from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, MessagesState, START, END

workflow = StateGraph(state_schema=MessagesState)

def call_model(state:MessagesState):
    print(state)
    print(type(state))
    response = llm.invoke(state["messages"])
    return {"messages": response}

workflow.add_edge(START, "model")
workflow.add_node("model", call_model)

memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

config = {"configurable": {"thread_id": "abc123"}}

query = "Hi! I'm Bob."

input_messages = [HumanMessage(query)]
output = app.invoke({"messages": input_messages}, config)
output["messages"][-1].pretty_print()  # output contains all messages in state

from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(base_url="https://api.siliconflow.cn/v1",
                              model="Qwen/Qwen3-Embedding-8B",
                              api_key="sk-cpsyzwrdctifhucukfycqpqvccbabkapwqabcyrttgpxgtth",)

vector_store = InMemoryVectorStore(embedding=embeddings)

import bs4
from langchain import hub
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import START, StateGraph
from typing_extensions import List, TypedDict

# Load and chunk contents of the blog
loader = WebBaseLoader(
    web_paths=("https://lilianweng.github.io/posts/2023-06-23-agent/",),
    bs_kwargs=dict(
        parse_only=bs4.SoupStrainer(
            class_=("post-content", "post-title", "post-header")
        )
    ),
)

docs = loader.load()
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=200)
all_splits = text_splitter.split_documents(docs)
_ = vector_store.add_documents(documents=all_splits)

from langsmith import Client
client = Client(api_key=os.environ["LANGSMITH_API_KEY"])
prompt = client.pull_prompt("rlm/rag-prompt", include_model=True)

print(prompt)

class State(TypedDict):
    question:str
    context:List[Document]
    answer:str

def retrieve(state:State):
    retrieved = vector_store.similarity_search(state['question'])
    return {"context": retrieved}

once = True

def generate(state:State):
    global once
    docs_content = "\n\n".join(doc.page_content for doc in state['context'])
    if once:
        once = False
        print("docs_content: ",docs_content)
    messages = prompt.invoke({"question": state['question'], "context": docs_content})
    response = llm.invoke(messages)
    return {"answer": response.content}

# Compile application and test
graph_builder = StateGraph(State).add_sequence([retrieve, generate])
graph_builder.add_edge(START, "retrieve")
graph = graph_builder.compile()


response = graph.invoke({"question": "What is Task Decomposition?"})
print(response["answer"])

